# 知识星球网页全屏插件

## 功能说明
本Chrome插件专为知识星球网站(https://wx.zsxq.com/)设计，提供以下功能：
- 自动检测页面中的视频元素
- 为视频添加网页全屏控制按钮
- 一键切换整个网页全屏模式
- 保持页面原有布局和功能
- 提供友好的用户反馈和通知
- 支持ESC键和退出按钮快速退出全屏

## 安装步骤
1. 下载插件代码
2. 在Chrome浏览器地址栏输入：`chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择本插件目录

## 使用说明
- 访问知识星球网站并打开包含视频的页面
- 插件会自动检测视频并添加全屏按钮（⛶）
- 点击全屏按钮进入网页全屏模式
- 点击右上角的"退出全屏"按钮或按ESC键可退出全屏模式

## 技术特点
- 使用MutationObserver监听动态加载的视频
- 多级选择器策略，提高DOM结构兼容性
- 分离的CSS样式文件，提高代码可维护性
- 完善的错误处理和用户通知机制
- 优雅的动画过渡效果

## 注意事项
- 需要Chrome 80或更高版本
- 仅适用于https://wx.zsxq.com/域名
- 如果网站更新DOM结构，插件会尝试使用备选选择器
- 首次使用可能需要刷新页面

## 文件结构
```
├── manifest.json    # 插件配置文件
├── content.js       # 核心功能脚本
├── styles.css       # 样式文件
└── icons/           # 插件图标目录
    ├── icon16.png
    ├── icon48.png
    └── icon128.png
```

## 版本历史
- v1.3: 代码重构，增强稳定性，添加用户通知
- v1.2: 修复控制栏显示问题
- v1.1: 添加ESC键支持
- v1.0: 初始版本

## 开发说明
- 主要功能在content.js中实现
- 样式定义在styles.css中
- 欢迎提交Issue或Pull Request